package com.tqhit.battery.one.features.emoji.presentation.customize

import android.content.Context
import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Effect handler for the Customize screen that manages side effects.
 * This class handles all side effects like saving customization, permission requests,
 * accessibility service management, navigation, and user feedback.
 * 
 * This separates side effect logic from the ViewModel and makes it more testable.
 * Uses dependency injection to access required services and repositories.
 */
@Singleton
class CustomizeEffectHandler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val customizationRepository: CustomizationRepository,
    private val appRepository: AppRepository
) {
    
    companion object {
        private const val TAG = "CustomizeEffectHandler"
    }
    
    private val _effects = MutableSharedFlow<CustomizeEffect>()
    val effects: SharedFlow<CustomizeEffect> = _effects.asSharedFlow()
    
    /**
     * Handles style selection with analytics
     */
    fun handleStyleSelection(
        style: BatteryStyle,
        styleType: String,
        coroutineScope: CoroutineScope
    ) {
        Log.d(TAG, "EFFECT: Handling style selection: ${style.name} (type: $styleType)")
        
        coroutineScope.launch {
            _effects.emit(
                CustomizeEffect.Analytics.TrackStyleSelected(
                    styleName = style.name,
                    styleType = styleType,
                    isPremium = style.isPremium
                )
            )
        }
    }
    
    /**
     * Handles customization option changes with analytics
     */
    fun handleCustomizationChanged(
        optionName: String,
        optionValue: String,
        coroutineScope: CoroutineScope
    ) {
        Log.d(TAG, "EFFECT: Handling customization change: $optionName = $optionValue")
        
        coroutineScope.launch {
            _effects.emit(
                CustomizeEffect.Analytics.TrackCustomizationChanged(
                    optionName = optionName,
                    optionValue = optionValue
                )
            )
        }
    }
    
    /**
     * Handles saving customization configuration
     */
    fun handleSaveCustomization(
        config: CustomizationConfig,
        coroutineScope: CoroutineScope,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        Log.d(TAG, "EFFECT: Handling save customization")
        
        coroutineScope.launch {
            try {
                val result = customizationRepository.saveCustomizationConfig(config)
                
                if (result.isSuccess) {
                    Log.d(TAG, "EFFECT: Customization saved successfully")
                    onSuccess()
                    _effects.emit(
                        CustomizeEffect.UserFeedback.ShowSuccessMessage(
                            "Customization saved successfully"
                        )
                    )
                } else {
                    val errorMsg = "Failed to save customization"
                    Log.e(TAG, "EFFECT: $errorMsg", result.exceptionOrNull())
                    onError(errorMsg)
                    _effects.emit(CustomizeEffect.UserFeedback.ShowErrorMessage(errorMsg))
                }
            } catch (exception: Exception) {
                val errorMsg = "Error saving customization"
                Log.e(TAG, "EFFECT: $errorMsg", exception)
                onError(errorMsg)
                _effects.emit(CustomizeEffect.UserFeedback.ShowErrorMessage(errorMsg))
            }
        }
    }
    
    /**
     * Handles applying customization with permission checks
     */
    fun handleApplyCustomization(
        config: CustomizationConfig,
        coroutineScope: CoroutineScope,
        onSuccess: () -> Unit,
        onError: (String) -> Unit,
        onPermissionRequired: () -> Unit
    ) {
        Log.d(TAG, "EFFECT: Handling apply customization")
        
        coroutineScope.launch {
            try {
                // Check permissions first
                if (!EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
                    Log.d(TAG, "EFFECT: Permissions required for applying customization")
                    onPermissionRequired()
                    _effects.emit(CustomizeEffect.SystemInteraction.RequestAccessibilityPermissions)
                    return@launch
                }
                
                // Save configuration with global enabled
                val enabledConfig = config.copy(isGlobalEnabled = true)
                val result = customizationRepository.saveCustomizationConfig(enabledConfig)
                
                if (result.isSuccess) {
                    Log.d(TAG, "EFFECT: Customization applied successfully")
                    onSuccess()
                    
                    _effects.emit(
                        CustomizeEffect.UserFeedback.ShowSuccessMessage(
                            "Emoji battery customization applied!"
                        )
                    )
                    
                    _effects.emit(CustomizeEffect.UIInteraction.AnimateApplySuccess)
                    
                    // Track analytics
                    _effects.emit(
                        CustomizeEffect.Analytics.TrackCustomizationApplied(
                            batteryStyleName = config.batteryStyleName,
                            emojiStyleName = config.emojiStyleName,
                            isPremium = false // Will be updated based on actual premium status
                        )
                    )
                    
                    // Navigate back after short delay
                    _effects.emit(CustomizeEffect.Navigation.NavigateBack)
                    
                } else {
                    val errorMsg = "Failed to apply customization"
                    Log.e(TAG, "EFFECT: $errorMsg", result.exceptionOrNull())
                    onError(errorMsg)
                    _effects.emit(CustomizeEffect.UserFeedback.ShowErrorMessage(errorMsg))
                    _effects.emit(CustomizeEffect.UIInteraction.AnimateApplyError)
                }
                
            } catch (exception: Exception) {
                val errorMsg = "Error applying customization"
                Log.e(TAG, "EFFECT: $errorMsg", exception)
                onError(errorMsg)
                _effects.emit(CustomizeEffect.UserFeedback.ShowErrorMessage(errorMsg))
                _effects.emit(CustomizeEffect.UIInteraction.AnimateApplyError)
            }
        }
    }
    
    /**
     * Handles premium customization with reward ad
     */
    fun handlePremiumCustomization(
        config: CustomizationConfig,
        coroutineScope: CoroutineScope
    ) {
        Log.d(TAG, "EFFECT: Handling premium customization with reward ad")

        coroutineScope.launch {
            _effects.emit(CustomizeEffect.SystemInteraction.ShowRewardAd(config))

            _effects.emit(
                CustomizeEffect.Analytics.TrackPremiumUnlockAttempt(
                    styleName = "${config.batteryStyleName} + ${config.emojiStyleName}",
                    method = "reward_ad"
                )
            )
        }
    }

    /**
     * Handles applying customization after reward ad completion
     * This bypasses premium checks since the user has earned access via the ad
     */
    fun handleApplyCustomizationAfterRewardAd(
        config: CustomizationConfig,
        coroutineScope: CoroutineScope,
        onSuccess: () -> Unit,
        onError: (String) -> Unit,
        onPermissionRequired: () -> Unit
    ) {
        Log.d(TAG, "EFFECT: Handling apply customization after reward ad completion")

        coroutineScope.launch {
            try {
                // Check permissions first
                if (!EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
                    Log.d(TAG, "EFFECT: Permissions required for applying customization after reward ad")
                    onPermissionRequired()
                    _effects.emit(CustomizeEffect.SystemInteraction.RequestAccessibilityPermissions)
                    return@launch
                }

                // Save configuration with global enabled
                val enabledConfig = config.copy(isGlobalEnabled = true)
                val result = customizationRepository.saveCustomizationConfig(enabledConfig)

                if (result.isSuccess) {
                    Log.d(TAG, "EFFECT: Premium customization applied successfully after reward ad")
                    onSuccess()

                    _effects.emit(
                        CustomizeEffect.UserFeedback.ShowSuccessMessage(
                            "Premium emoji battery customization applied!"
                        )
                    )

                    _effects.emit(CustomizeEffect.UIInteraction.AnimateApplySuccess)

                    // Track analytics for premium unlock
                    _effects.emit(
                        CustomizeEffect.Analytics.TrackCustomizationApplied(
                            batteryStyleName = config.batteryStyleName,
                            emojiStyleName = config.emojiStyleName,
                            isPremium = true
                        )
                    )

                    _effects.emit(
                        CustomizeEffect.Analytics.TrackPremiumUnlockSuccess(
                            styleName = "${config.batteryStyleName} + ${config.emojiStyleName}",
                            method = "reward_ad"
                        )
                    )

                    // Navigate back after short delay
                    _effects.emit(CustomizeEffect.Navigation.NavigateBack)

                } else {
                    val errorMsg = "Failed to apply premium customization"
                    Log.e(TAG, "EFFECT: $errorMsg", result.exceptionOrNull())
                    onError(errorMsg)
                    _effects.emit(CustomizeEffect.UserFeedback.ShowErrorMessage(errorMsg))
                    _effects.emit(CustomizeEffect.UIInteraction.AnimateApplyError)
                }

            } catch (exception: Exception) {
                val errorMsg = "Error applying premium customization"
                Log.e(TAG, "EFFECT: $errorMsg", exception)
                onError(errorMsg)
                _effects.emit(CustomizeEffect.UserFeedback.ShowErrorMessage(errorMsg))
                _effects.emit(CustomizeEffect.UIInteraction.AnimateApplyError)
            }
        }
    }
    
    /**
     * Handles permission request results
     */
    fun handlePermissionResult(
        granted: Boolean,
        coroutineScope: CoroutineScope,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        Log.d(TAG, "EFFECT: Handling permission result: $granted")
        
        coroutineScope.launch {
            if (granted) {
                Log.d(TAG, "EFFECT: Permissions granted, proceeding with customization")
                onGranted()
                _effects.emit(
                    CustomizeEffect.PermissionFlow.ShowPermissionGranted("accessibility")
                )
            } else {
                Log.d(TAG, "EFFECT: Permissions denied")
                onDenied()
                _effects.emit(
                    CustomizeEffect.PermissionFlow.ShowPermissionDenied(
                        permissionType = "accessibility",
                        canRetry = true
                    )
                )
                _effects.emit(
                    CustomizeEffect.UserFeedback.ShowErrorMessage(
                        "Permissions required to apply emoji battery customization"
                    )
                )
            }
        }
    }
    
    /**
     * Handles navigation back
     */
    fun handleNavigateBack(coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling navigate back")
        
        coroutineScope.launch {
            _effects.emit(CustomizeEffect.Navigation.NavigateBack)
        }
    }
    
    /**
     * Handles color picker visibility
     */
    fun handleColorPickerVisibility(
        isVisible: Boolean,
        coroutineScope: CoroutineScope
    ) {
        Log.d(TAG, "EFFECT: Handling color picker visibility: $isVisible")
        
        coroutineScope.launch {
            if (isVisible) {
                _effects.emit(CustomizeEffect.UIInteraction.ShowColorPicker)
            } else {
                _effects.emit(CustomizeEffect.UIInteraction.HideColorPicker)
            }
        }
    }
    
    /**
     * Handles preview updates
     */
    fun handlePreviewUpdate(
        config: CustomizationConfig,
        coroutineScope: CoroutineScope
    ) {
        Log.d(TAG, "EFFECT: Handling preview update")
        
        coroutineScope.launch {
            _effects.emit(CustomizeEffect.UIInteraction.UpdatePreview(config))
        }
    }
    
    /**
     * Handles error display
     */
    fun handleError(
        errorMessage: String,
        errorType: String,
        coroutineScope: CoroutineScope
    ) {
        Log.e(TAG, "EFFECT: Handling error: $errorMessage (type: $errorType)")
        
        coroutineScope.launch {
            _effects.emit(CustomizeEffect.UserFeedback.ShowErrorMessage(errorMessage))
            
            _effects.emit(
                CustomizeEffect.Analytics.TrackError(
                    errorType = errorType,
                    errorMessage = errorMessage
                )
            )
        }
    }
    
    /**
     * Handles success messages
     */
    fun handleSuccess(message: String, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling success: $message")
        
        coroutineScope.launch {
            _effects.emit(CustomizeEffect.UserFeedback.ShowSuccessMessage(message))
        }
    }
    
    /**
     * Handles validation errors
     */
    fun handleValidationError(message: String, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling validation error: $message")
        
        coroutineScope.launch {
            _effects.emit(CustomizeEffect.UserFeedback.ShowValidationError(message))
        }
    }
    
    /**
     * Handles screen view tracking
     */
    fun handleScreenView(screenName: String, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling screen view: $screenName")
        
        coroutineScope.launch {
            _effects.emit(CustomizeEffect.Analytics.TrackScreenView(screenName))
        }
    }
    
    /**
     * Handles user engagement tracking
     */
    fun handleEngagement(action: String, timeSpentMs: Long, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling engagement: $action (${timeSpentMs}ms)")
        
        coroutineScope.launch {
            _effects.emit(
                CustomizeEffect.Analytics.TrackEngagement(
                    action = action,
                    timeSpentMs = timeSpentMs
                )
            )
        }
    }
}
