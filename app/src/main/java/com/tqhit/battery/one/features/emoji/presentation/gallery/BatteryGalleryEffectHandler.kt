package com.tqhit.battery.one.features.emoji.presentation.gallery

import android.content.Context
import android.util.Log
import androidx.lifecycle.viewModelScope
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Effect handler for the Battery Gallery screen that manages side effects.
 * This class handles all side effects like permission requests, navigation, user feedback,
 * and external service interactions.
 * 
 * This separates side effect logic from the ViewModel and makes it more testable.
 * Uses dependency injection to access required services and repositories.
 */
@Singleton
class BatteryGalleryEffectHandler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val customizationRepository: CustomizationRepository,
    private val appRepository: AppRepository
) {
    
    companion object {
        private const val TAG = "BatteryGalleryEffectHandler"
    }
    
    private val _effects = MutableSharedFlow<BatteryGalleryEffect>()
    val effects: SharedFlow<BatteryGalleryEffect> = _effects.asSharedFlow()
    
    /**
     * Handles battery style selection and navigation
     */
    fun handleStyleSelection(style: BatteryStyle, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling style selection: ${style.name}, isPremium: ${style.isPremium}")
        
        coroutineScope.launch {
            // Always navigate to customization screen regardless of premium status
            // Premium restrictions are handled at the apply button level
            Log.d(TAG, "EFFECT: Emitting navigate to customize effect for: ${style.name}")
            _effects.emit(BatteryGalleryEffect.Navigation.NavigateToCustomize(style))
            
            // Track analytics
            _effects.emit(
                BatteryGalleryEffect.Analytics.TrackStyleSelected(
                    styleName = style.name,
                    isPremium = style.isPremium
                )
            )
        }
    }
    
    /**
     * Handles global feature toggle
     */
    fun handleGlobalFeatureToggle(
        isEnabled: Boolean,
        coroutineScope: CoroutineScope,
        onStateUpdate: (Boolean) -> Unit
    ) {
        Log.d(TAG, "EFFECT: Handling global feature toggle: $isEnabled")
        
        if (isEnabled) {
            // Check permissions before enabling
            if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
                enableOverlayFeature(coroutineScope, onStateUpdate)
            } else {
                // Reset toggle state and request permissions
                onStateUpdate(false)
                requestPermissions(coroutineScope)
            }
        } else {
            // Disable the feature
            disableOverlayFeature(coroutineScope, onStateUpdate)
        }
    }
    
    /**
     * Handles permission request results
     */
    fun handlePermissionResult(
        granted: Boolean,
        coroutineScope: CoroutineScope,
        onStateUpdate: (Boolean) -> Unit,
        onError: (String) -> Unit
    ) {
        Log.d(TAG, "EFFECT: Handling permission result: $granted")
        
        if (granted) {
            enableOverlayFeature(coroutineScope, onStateUpdate)
        } else {
            onStateUpdate(false)
            onError("Permissions required for emoji battery feature")
            
            coroutineScope.launch {
                _effects.emit(
                    BatteryGalleryEffect.UserFeedback.ShowErrorMessage(
                        "Permissions required for emoji battery feature"
                    )
                )
            }
        }
    }
    
    /**
     * Handles premium style unlock
     */
    fun handlePremiumUnlock(style: BatteryStyle, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling premium unlock for: ${style.name}")
        
        coroutineScope.launch {
            _effects.emit(BatteryGalleryEffect.Dialog.ShowPremiumDialog(style))
            _effects.emit(
                BatteryGalleryEffect.Analytics.TrackPremiumUnlockAttempt(
                    styleName = style.name,
                    method = "direct"
                )
            )
        }
    }
    
    /**
     * Handles ad-based unlock
     */
    fun handleAdUnlock(style: BatteryStyle, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling ad unlock for: ${style.name}")
        
        coroutineScope.launch {
            _effects.emit(BatteryGalleryEffect.SystemInteraction.ShowRewardAd(style))
            _effects.emit(
                BatteryGalleryEffect.Analytics.TrackPremiumUnlockAttempt(
                    styleName = style.name,
                    method = "reward_ad"
                )
            )
        }
    }
    
    /**
     * Handles error display
     */
    fun handleError(
        errorMessage: String,
        errorType: ErrorType,
        isRetryable: Boolean,
        coroutineScope: CoroutineScope
    ) {
        Log.e(TAG, "EFFECT: Handling error: $errorMessage (type: $errorType)")
        
        coroutineScope.launch {
            _effects.emit(
                BatteryGalleryEffect.Dialog.ShowErrorDialog(
                    message = errorMessage,
                    isRetryable = isRetryable
                )
            )
            
            _effects.emit(
                BatteryGalleryEffect.Analytics.TrackError(
                    errorType = errorType.name,
                    errorMessage = errorMessage
                )
            )
        }
    }
    
    /**
     * Handles success messages
     */
    fun handleSuccess(message: String, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling success: $message")
        
        coroutineScope.launch {
            _effects.emit(BatteryGalleryEffect.UserFeedback.ShowSuccessMessage(message))
        }
    }
    
    /**
     * Handles category selection analytics
     */
    fun handleCategorySelection(categoryName: String, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling category selection: $categoryName")
        
        coroutineScope.launch {
            _effects.emit(
                BatteryGalleryEffect.Analytics.TrackCategorySelected(categoryName)
            )
        }
    }
    
    /**
     * Handles search analytics
     */
    fun handleSearchPerformed(query: String, resultsCount: Int, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling search: '$query' with $resultsCount results")
        
        coroutineScope.launch {
            _effects.emit(
                BatteryGalleryEffect.Analytics.TrackSearchPerformed(
                    query = query,
                    resultsCount = resultsCount
                )
            )
        }
    }
    
    /**
     * Handles data refresh
     */
    fun handleDataRefresh(coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Handling data refresh")

        coroutineScope.launch {
            _effects.emit(BatteryGalleryEffect.DataOperation.RefreshData)
        }
    }

    /**
     * Handles showing permission dialog
     */
    fun handleShowPermissionDialog(coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Showing permission dialog")

        coroutineScope.launch {
            _effects.emit(BatteryGalleryEffect.Dialog.ShowPermissionDialog)
        }
    }

    /**
     * Handles showing info dialog
     */
    fun handleShowInfoDialog(coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Showing info dialog")

        coroutineScope.launch {
            _effects.emit(BatteryGalleryEffect.Dialog.ShowInfoDialog)
        }
    }

    /**
     * Handles showing toast message
     */
    fun handleShowToast(message: String, coroutineScope: CoroutineScope) {
        Log.d(TAG, "EFFECT: Showing toast: $message")

        coroutineScope.launch {
            _effects.emit(BatteryGalleryEffect.UserFeedback.ShowToast(message))
        }
    }
    
    // Private helper methods
    
    private fun enableOverlayFeature(
        coroutineScope: CoroutineScope,
        onStateUpdate: (Boolean) -> Unit
    ) {
        coroutineScope.launch {
            try {
                Log.d(TAG, "EFFECT: Enabling overlay feature")
                val result = customizationRepository.updateGlobalEnabled(true)
                
                if (result.isSuccess) {
                    onStateUpdate(true)
                    _effects.emit(
                        BatteryGalleryEffect.UserFeedback.ShowSuccessMessage(
                            "Emoji battery feature enabled"
                        )
                    )
                    Log.d(TAG, "EFFECT: Overlay feature enabled successfully")
                } else {
                    onStateUpdate(false)
                    val errorMsg = "Failed to enable emoji battery feature"
                    Log.e(TAG, "EFFECT: $errorMsg", result.exceptionOrNull())
                    _effects.emit(BatteryGalleryEffect.UserFeedback.ShowErrorMessage(errorMsg))
                }
            } catch (exception: Exception) {
                onStateUpdate(false)
                val errorMsg = "Error enabling emoji battery feature"
                Log.e(TAG, "EFFECT: $errorMsg", exception)
                _effects.emit(BatteryGalleryEffect.UserFeedback.ShowErrorMessage(errorMsg))
            }
        }
    }
    
    private fun disableOverlayFeature(
        coroutineScope: CoroutineScope,
        onStateUpdate: (Boolean) -> Unit
    ) {
        coroutineScope.launch {
            try {
                Log.d(TAG, "EFFECT: Disabling overlay feature")
                val result = customizationRepository.updateGlobalEnabled(false)
                
                if (result.isSuccess) {
                    onStateUpdate(false)
                    _effects.emit(
                        BatteryGalleryEffect.UserFeedback.ShowSuccessMessage(
                            "Emoji battery feature disabled"
                        )
                    )
                    Log.d(TAG, "EFFECT: Overlay feature disabled successfully")
                } else {
                    val errorMsg = "Failed to disable emoji battery feature"
                    Log.e(TAG, "EFFECT: $errorMsg", result.exceptionOrNull())
                    _effects.emit(BatteryGalleryEffect.UserFeedback.ShowErrorMessage(errorMsg))
                }
            } catch (exception: Exception) {
                val errorMsg = "Error disabling emoji battery feature"
                Log.e(TAG, "EFFECT: $errorMsg", exception)
                _effects.emit(BatteryGalleryEffect.UserFeedback.ShowErrorMessage(errorMsg))
            }
        }
    }
    
    private fun requestPermissions(coroutineScope: CoroutineScope) {
        coroutineScope.launch {
            Log.d(TAG, "EFFECT: Requesting permissions")
            _effects.emit(BatteryGalleryEffect.SystemInteraction.RequestAccessibilityPermissions)
        }
    }
}
