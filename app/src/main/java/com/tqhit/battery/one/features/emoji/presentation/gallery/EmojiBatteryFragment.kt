package com.tqhit.battery.one.features.emoji.presentation.gallery

import android.content.res.Resources
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentEmojiBatteryBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.model.toCategoryId
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCategory
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.CategoryAdapter
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.CategoryViewHolder
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.GridSpacingItemDecoration
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Fragment for displaying the emoji battery gallery.
 * Shows available battery styles organized by categories with search and filter functionality.
 * 
 * This fragment follows the established patterns in the app:
 * - Extends AdLibBaseFragment for ad integration
 * - Uses ViewBinding for view access
 * - Implements MVI pattern with ViewModel
 * - Uses Hilt for dependency injection
 * - Follows Material 3 design guidelines
 */
@AndroidEntryPoint
class EmojiBatteryFragment : AdLibBaseFragment<FragmentEmojiBatteryBinding>() {
    
    companion object {
        private const val TAG = "EmojiBatteryFragment"
        
        fun newInstance(): EmojiBatteryFragment {
            return EmojiBatteryFragment()
        }
    }
    
    // ViewBinding
    override val binding by lazy { FragmentEmojiBatteryBinding.inflate(layoutInflater) }
    
    // ViewModel
    private val viewModel: BatteryGalleryViewModel by viewModels()
    
    // Dependencies
    @Inject
    lateinit var appRepository: AppRepository
    
    // Adapters
    private lateinit var categoryAdapter: CategoryAdapter
    private lateinit var batteryStyleAdapter: BatteryStyleAdapter
    
    // State
    private var selectedCategoryIndex = 0
    private var categories = BatteryStyleCategory.getMainFilterCategories() // Changed to var for remote config updates
    private var isUsingRemoteConfigCategories = false // Track data source
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "EMOJI_FRAGMENT: onCreate called - savedInstanceState: ${savedInstanceState != null}")
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        Log.d(TAG, "EMOJI_FRAGMENT: onCreateView called")
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "EMOJI_FRAGMENT: onViewCreated called")
    }

    override fun setupUI() {
        Log.d(TAG, "EMOJI_FRAGMENT: setupUI called")
        super.setupUI()

        setupUIComponents()
        setupRecyclerViews()
        setupClickListeners()
        setupSearchFunctionality()
        observeViewModel()

        // Load initial data
        Log.d(TAG, "EMOJI_FRAGMENT: Triggering LoadInitialData event")
        viewModel.handleEvent(BatteryGalleryEvent.LoadInitialData)
        Log.d(TAG, "EMOJI_FRAGMENT: setupUI completed")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "EMOJI_FRAGMENT: onResume called")
        
        // Check if permissions were granted when returning from settings
        checkPermissionsAfterResume()
        
        viewModel.handleEvent(BatteryGalleryEvent.SystemEvent.OnResume)
    }
    
    /**
     * Checks if permissions were granted when returning from settings
     */
    private fun checkPermissionsAfterResume() {
        Log.d(TAG, "EMOJI_PERMISSION: Checking permissions after resume")
        
        // Only check if we were previously waiting for permissions
        val currentState = viewModel.uiState.value
        if (currentState.shouldRequestPermissions || !currentState.isGlobalToggleEnabled) {
            Log.d(TAG, "EMOJI_PERMISSION: Previous state indicates permission check needed")
            
            if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(requireContext())) {
                Log.d(TAG, "EMOJI_PERMISSION: Permissions now granted, enabling feature")
                viewModel.handlePermissionResult(granted = true)
            } else {
                Log.d(TAG, "EMOJI_PERMISSION: Permissions still missing")
                // Permission status will be logged by the permission manager
                val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(requireContext())
                Log.d(TAG, "EMOJI_PERMISSION: Current permission status: $permissionStatus")
            }
        }
    }

    override fun onDestroyView() {
        // Clean up RecyclerView to prevent memory leaks and state issues
        resetRecyclerView()

        // Clean up category adapter animations
        cleanupCategoryAnimations()

        super.onDestroyView()
    }

    /**
     * Cleans up category adapter resources (simplified for consistency with Animation fragment)
     */
    private fun cleanupCategoryAnimations() {
        // No cleanup needed for simple drawable switching approach
        Log.d(TAG, "Category adapter cleanup completed")
    }


    
    /**
     * Sets up the UI components
     */
    private fun setupUIComponents() {
        try {
            // Setup banner ad
            setupBannerAd()

            // Setup pull-to-refresh (commented out for now due to dependency issue)
            // binding.swipeRefreshLayout.setOnRefreshListener {
            //     viewModel.handleEvent(BatteryGalleryEvent.RefreshData)
            // }

            Log.d(TAG, "UI components setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up UI components", exception)
        }
    }
    
    /**
     * Sets up the RecyclerViews for categories and battery styles
     */
    private fun setupRecyclerViews() {
        try {
            setupCategoryRecyclerView()
            setupBatteryStyleRecyclerView()
            Log.d(TAG, "RecyclerViews setup completed")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up RecyclerViews", exception)
        }
    }
    
    /**
     * Sets up the category RecyclerView with enhanced visual feedback
     */
    private fun setupCategoryRecyclerView() {
        Log.d(TAG, "REMOTE_CONFIG: Setting up CategoryRecyclerView with ${categories.size} categories")
        Log.d(TAG, "REMOTE_CONFIG: Initial categories: ${categories.map { it.displayName }}")
        Log.d(TAG, "REMOTE_CONFIG: Using remote config categories: $isUsingRemoteConfigCategories")

        categoryAdapter = CategoryAdapter(selectedCategoryIndex) { index ->
            Log.d(TAG, "Category selected: $index with enhanced visual feedback")
            Log.i(TAG, "EMOJI_LOADING_FLOW: ========== USER CATEGORY SELECTION ==========")
            Log.i(TAG, "EMOJI_LOADING_FLOW: User clicked category at index: $index")

            // Provide immediate visual feedback before state update
            if (index != selectedCategoryIndex) {
                val oldIndex = selectedCategoryIndex
                selectedCategoryIndex = index
                val selectedCategory = categories[index]

                Log.i(TAG, "EMOJI_LOADING_FLOW: Category changed from '${categories[oldIndex].displayName}' to '${selectedCategory.displayName}'")

                // Immediately update adapter for instant visual feedback
                categoryAdapter.updateSelection(selectedCategoryIndex)

                // Trigger ViewModel event for state management
                viewModel.handleEvent(BatteryGalleryEvent.SelectCategory(selectedCategory))

                Log.d(TAG, "Category selection completed: ${selectedCategory.displayName} (immediate visual update from $oldIndex to $selectedCategoryIndex)")
                Log.i(TAG, "EMOJI_LOADING_FLOW: Triggered ViewModel event for category: ${selectedCategory.displayName}")
            } else {
                Log.i(TAG, "EMOJI_LOADING_FLOW: Same category selected, no change needed")
            }
        }

        // Submit the initial list to the adapter
        categoryAdapter.submitList(categories)

        binding.categoryRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
            adapter = categoryAdapter

            // Enable smooth scrolling for better UX
            isNestedScrollingEnabled = true

            // Add item decoration for better spacing if needed
            // addItemDecoration(CategoryItemDecoration())
        }

        Log.d(TAG, "REMOTE_CONFIG: CategoryRecyclerView setup completed with adapter containing ${categoryAdapter.itemCount} items")
    }
    
    /**
     * Sets up the battery style RecyclerView
     */
    private fun setupBatteryStyleRecyclerView() {
        Log.d(TAG, "EMOJI_FRAGMENT: Setting up battery style RecyclerView")

        // Force a complete reset of the RecyclerView to prevent any state issues
        resetRecyclerView()

        // Set up fresh layout manager
        val layoutManager = GridLayoutManager(requireContext(), 3)
        binding.emojiRecyclerView.layoutManager = layoutManager
        Log.d(TAG, "EMOJI_FRAGMENT: Layout manager set to GridLayoutManager with 3 columns")

        val spacing = (8 * Resources.getSystem().displayMetrics.density).toInt() // 8dp to px (optimized for square cards)
        binding.emojiRecyclerView.addItemDecoration(
            GridSpacingItemDecoration(3, spacing)
        )
        Log.d(TAG, "EMOJI_FRAGMENT: Item decoration added with spacing: ${spacing}px")

        // Create new adapter instance to prevent state issues
        batteryStyleAdapter = BatteryStyleAdapter(
            requireActivity(),
            onStyleClick = { style ->
                Log.d(TAG, "EMOJI_FRAGMENT: Battery style clicked callback triggered: ${style.name}")
                Log.d(TAG, "PREMIUM_NAVIGATION_FIX: Navigating to customization for ${if (style.isPremium) "premium" else "free"} style: ${style.name}")
                viewModel.handleEvent(BatteryGalleryEvent.SelectBatteryStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "EMOJI_FRAGMENT: Premium unlock requested callback triggered: ${style.name}")
                viewModel.handleEvent(BatteryGalleryEvent.UnlockPremiumStyle(style))
            }
        )

        binding.emojiRecyclerView.adapter = batteryStyleAdapter

        // Log RecyclerView configuration
        Log.d(TAG, "EMOJI_FRAGMENT: RecyclerView setup completed")
        Log.d(TAG, "EMOJI_FRAGMENT: - Adapter: ${binding.emojiRecyclerView.adapter}")
        Log.d(TAG, "EMOJI_FRAGMENT: - Layout Manager: ${binding.emojiRecyclerView.layoutManager}")
        Log.d(TAG, "EMOJI_FRAGMENT: - Visibility: ${binding.emojiRecyclerView.visibility}")
        Log.d(TAG, "EMOJI_FRAGMENT: - Width: ${binding.emojiRecyclerView.width}, Height: ${binding.emojiRecyclerView.height}")

        // Force a layout pass to ensure proper setup
        binding.emojiRecyclerView.post {
            Log.d(TAG, "EMOJI_FRAGMENT: Post-layout - Width: ${binding.emojiRecyclerView.width}, Height: ${binding.emojiRecyclerView.height}")
            Log.d(TAG, "EMOJI_FRAGMENT: Post-layout - Child count: ${binding.emojiRecyclerView.childCount}")
            Log.d(TAG, "EMOJI_FRAGMENT: Post-layout - Adapter item count: ${binding.emojiRecyclerView.adapter?.itemCount}")
        }
    }

    /**
     * Completely resets the RecyclerView to prevent any state issues
     */
    private fun resetRecyclerView() {
        try {
            // Stop any ongoing animations
            binding.emojiRecyclerView.itemAnimator?.endAnimations()

            // Clear adapter first
            binding.emojiRecyclerView.adapter = null

            // Clear layout manager
            binding.emojiRecyclerView.layoutManager = null

            // Remove all item decorations
            while (binding.emojiRecyclerView.itemDecorationCount > 0) {
                binding.emojiRecyclerView.removeItemDecorationAt(0)
            }

            // Clear any cached views
            binding.emojiRecyclerView.recycledViewPool.clear()

            // Force a layout pass to ensure clean state
            binding.emojiRecyclerView.requestLayout()
        } catch (exception: Exception) {
            Log.e(TAG, "Error during RecyclerView reset", exception)
        }
    }
    
    /**
     * Sets up click listeners for UI components
     */
    private fun setupClickListeners() {
        // Global toggle switch
        binding.globalToggleSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Global toggle changed: $isChecked")
            viewModel.handleEvent(BatteryGalleryEvent.ToggleGlobalFeature(isChecked))
        }
        
        // Search button
        binding.searchButton.setOnClickListener {
            Log.d(TAG, "Search button clicked")
            viewModel.handleEvent(BatteryGalleryEvent.ToggleSearchMode)
        }
        
        // Clear search button
        binding.clearSearchButton.setOnClickListener {
            Log.d(TAG, "Clear search button clicked")
            viewModel.handleEvent(BatteryGalleryEvent.ClearSearch)
        }
        
        // Filter button
        binding.filterButton.setOnClickListener {
            Log.d(TAG, "Filter button clicked")
            showFilterOptions()
        }
        
        // Refresh button
        binding.refreshButton.setOnClickListener {
            Log.d(TAG, "Refresh button clicked")
            viewModel.handleEvent(BatteryGalleryEvent.RefreshData)
        }
        
        // Retry button
        binding.retryButton.setOnClickListener {
            Log.d(TAG, "Retry button clicked")
            viewModel.handleEvent(BatteryGalleryEvent.RetryLoad)
        }
        
        // Info button
        binding.emojiInfo.setOnClickListener {
            Log.d(TAG, "Info button clicked")
            showInfoDialog()
        }

        // Debug: Add long click to test all items
        binding.emojiInfo.setOnLongClickListener {
            Log.d(TAG, "CLICK_DEBUG: Testing all items navigation")
            testAllItemsNavigation()
            true
        }
    }
    
    /**
     * Sets up search functionality
     */
    private fun setupSearchFunctionality() {
        binding.searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val query = s?.toString() ?: ""
                Log.d(TAG, "Search query changed: $query")
                viewModel.handleEvent(BatteryGalleryEvent.SearchQueryChanged(query))
            }
            
            override fun afterTextChanged(s: Editable?) {}
        })
    }
    
    /**
     * Observes ViewModel state and effects - refactored for new MVI architecture
     */
    private fun observeViewModel() {
        Log.d(TAG, "REFACTORED_FRAGMENT: Setting up ViewModel observers with new state and effects")

        // Observe UI state changes
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "REFACTORED_FRAGMENT: Starting to collect UI state from ViewModel")
                viewModel.uiState.collect { state ->
                    Log.d(TAG, "REFACTORED_FRAGMENT: UI state collected - loading=${state.isLoading}, styles=${state.displayedStyles.size}")
                    Log.d(TAG, "REFACTORED_FRAGMENT: State details - allStyles=${state.allStyles.size}, selectedCategory=${state.selectedCategory.displayName}")
                    Log.d(TAG, "REFACTORED_FRAGMENT: State details - isInitialLoadComplete=${state.isInitialLoadComplete}, errorMessage=${state.errorMessage}")

                    updateUI(state)
                }
            }
        }

        // Observe effects separately - NEW: Handle effects instead of navigation events in state
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "REFACTORED_FRAGMENT: Starting to collect effects from ViewModel")
                viewModel.effects.collect { effect ->
                    Log.d(TAG, "REFACTORED_FRAGMENT: Effect received: ${effect::class.simpleName}")
                    handleEffect(effect)
                }
            }
        }

        // Observe emoji categories from remote config
        observeEmojiCategories()

        Log.d(TAG, "REFACTORED_FRAGMENT: ViewModel observers setup completed")
    }

    /**
     * Observes emoji categories from Firebase Remote Config.
     * Updates category tabs when remote config data is available.
     */
    private fun observeEmojiCategories() {
        Log.d(TAG, "REMOTE_CONFIG: Setting up emoji categories observation")

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "REMOTE_CONFIG: Starting to collect emoji categories from ViewModel")

                viewModel.emojiCategories.collect { remoteCategories ->
                    Log.d(TAG, "REMOTE_CONFIG: StateFlow emitted ${remoteCategories.size} categories from remote config")
                    Log.d(TAG, "REMOTE_CONFIG: Current categories in fragment: ${categories.size} (isUsingRemoteConfig: $isUsingRemoteConfigCategories)")

                    // Log current adapter state
                    if (::categoryAdapter.isInitialized) {
                        Log.d(TAG, "REMOTE_CONFIG: CategoryAdapter initialized with ${categoryAdapter.itemCount} items")
                    } else {
                        Log.d(TAG, "REMOTE_CONFIG: CategoryAdapter not yet initialized")
                    }

                    if (remoteCategories.isNotEmpty()) {
                        Log.d(TAG, "REMOTE_CONFIG: Processing remote categories for UI update")
                        updateCategoryTabs(remoteCategories)
                    } else {
                        Log.w(TAG, "REMOTE_CONFIG: No remote categories available, using hardcoded fallback")
                        Log.d(TAG, "REMOTE_CONFIG: Current hardcoded categories: ${categories.map { it.displayName }}")
                    }
                }
            }
        }
    }
    
    /**
     * Updates the UI based on the current state - refactored for new BatteryGalleryUiState
     */
    private fun updateUI(state: BatteryGalleryUiState) {
        try {
            Log.d(TAG, "REFACTORED_FRAGMENT: updateUI called with BatteryGalleryUiState:")
            Log.d(TAG, "REFACTORED_FRAGMENT: - isLoading: ${state.isLoading}")
            Log.d(TAG, "REFACTORED_FRAGMENT: - displayedStyles count: ${state.displayedStyles.size}")
            Log.d(TAG, "REFACTORED_FRAGMENT: - allStyles count: ${state.allStyles.size}")
            Log.d(TAG, "REFACTORED_FRAGMENT: - selectedCategory: ${state.selectedCategory.displayName}")
            Log.d(TAG, "REFACTORED_FRAGMENT: - errorMessage: ${state.errorMessage}")
            Log.d(TAG, "REFACTORED_FRAGMENT: - isInitialLoadComplete: ${state.isInitialLoadComplete}")
            Log.d(TAG, "REFACTORED_FRAGMENT: - isGlobalToggleEnabled: ${state.isGlobalToggleEnabled}")

            updateLoadingState(state)
            updateErrorState(state)
            updateEmptyState(state)
            updateSearchState(state)
            updateGlobalToggle(state)
            updateBatteryStyles(state)
            updateCategorySelection(state)
            updateRefreshState(state)

            Log.d(TAG, "REFACTORED_FRAGMENT: updateUI completed successfully")
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_FRAGMENT: Error updating UI", exception)
        }
    }

    /**
     * Handles effects from the ViewModel - NEW: Replaces navigation event handling
     */
    private fun handleEffect(effect: BatteryGalleryEffect) {
        Log.d(TAG, "REFACTORED_FRAGMENT: Handling effect: ${effect::class.simpleName}")

        when (effect) {
            is BatteryGalleryEffect.Navigation.NavigateToCustomize -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Navigating to customization for: ${effect.style.name}")
                navigateToCustomization(effect.style)
            }
            is BatteryGalleryEffect.Dialog.ShowPremiumDialog -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing premium dialog for: ${effect.style.name}")
                showPremiumUnlockDialog(effect.style)
            }
            is BatteryGalleryEffect.Dialog.ShowPermissionDialog -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing permission dialog")
                showPermissionDialog()
            }
            is BatteryGalleryEffect.Dialog.ShowInfoDialog -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing info dialog")
                showInfoDialog()
            }
            is BatteryGalleryEffect.Dialog.ShowErrorDialog -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing error dialog: ${effect.message}")
                showErrorDialog(effect.message, effect.isRetryable)
            }
            is BatteryGalleryEffect.UserFeedback.ShowToast -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing toast: ${effect.message}")
                showToast(effect.message)
            }
            is BatteryGalleryEffect.UserFeedback.ShowSnackbar -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing snackbar: ${effect.message}")
                showSnackbar(effect.message, effect.actionText, effect.action)
            }
            is BatteryGalleryEffect.UserFeedback.ShowSuccessMessage -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing success message: ${effect.message}")
                showSuccessMessage(effect.message)
            }
            is BatteryGalleryEffect.UserFeedback.ShowErrorMessage -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Showing error message: ${effect.message}")
                showErrorMessage(effect.message)
            }
            is BatteryGalleryEffect.SystemInteraction.RequestAccessibilityPermissions -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Requesting accessibility permissions")
                requestAccessibilityPermissions()
            }
            is BatteryGalleryEffect.SystemInteraction.OpenAccessibilitySettings -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Opening accessibility settings")
                openAccessibilitySettings()
            }
            else -> {
                Log.d(TAG, "REFACTORED_FRAGMENT: Unhandled effect: ${effect::class.simpleName}")
            }
        }
    }
    
    /**
     * Updates loading state - refactored for new BatteryGalleryUiState
     */
    private fun updateLoadingState(state: BatteryGalleryUiState) {
        binding.loadingContainer.visibility = if (state.isLoading && !state.isInitialLoadComplete) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    /**
     * Updates error state - refactored for new BatteryGalleryUiState
     */
    private fun updateErrorState(state: BatteryGalleryUiState) {
        if (state.hasError) {
            Log.d(TAG, "REFACTORED_FRAGMENT: Showing error state - type: ${state.errorType}, recoverable: ${state.isErrorRecoverable}")

            binding.errorContainer.visibility = View.VISIBLE
            binding.errorMessage.text = state.errorMessage
            binding.emojiRecyclerView.visibility = View.GONE

            // Update retry button based on error recoverability
            binding.retryButton.visibility = if (state.isErrorRecoverable) View.VISIBLE else View.GONE

            // Set up retry button click listener
            binding.retryButton.setOnClickListener {
                Log.d(TAG, "ERROR_UI: Retry button clicked for error type: ${state.errorType}")
                viewModel.retryLastOperation()
            }

            // Show appropriate error icon based on error type
            updateErrorIcon(state.errorType)

        } else {
            Log.d(TAG, "ERROR_UI: Hiding error state")
            binding.errorContainer.visibility = View.GONE
            binding.emojiRecyclerView.visibility = View.VISIBLE
        }
    }

    /**
     * Updates error icon based on error type
     */
    private fun updateErrorIcon(errorType: ErrorType?) {
        // TODO: Update error icon based on error type
        // For now, just log the error type
        Log.d(TAG, "ERROR_UI: Error type for icon: $errorType")

        when (errorType) {
            ErrorType.NETWORK_ERROR -> {
                Log.d(TAG, "ERROR_UI: Should show network error icon")
                // binding.errorIcon.setImageResource(R.drawable.ic_network_error)
            }
            ErrorType.REMOTE_CONFIG_ERROR -> {
                Log.d(TAG, "ERROR_UI: Should show remote config error icon")
                // binding.errorIcon.setImageResource(R.drawable.ic_cloud_error)
            }
            ErrorType.DATA_PARSING_ERROR -> {
                Log.d(TAG, "ERROR_UI: Should show data parsing error icon")
                // binding.errorIcon.setImageResource(R.drawable.ic_data_error)
            }
            ErrorType.PERMISSION_ERROR -> {
                Log.d(TAG, "ERROR_UI: Should show permission error icon")
                // binding.errorIcon.setImageResource(R.drawable.ic_permission_error)
            }
            else -> {
                Log.d(TAG, "ERROR_UI: Should show generic error icon")
                // binding.errorIcon.setImageResource(R.drawable.ic_error)
            }
        }
    }
    
    /**
     * Updates empty state - refactored for new BatteryGalleryUiState
     */
    private fun updateEmptyState(state: BatteryGalleryUiState) {
        val shouldShowEmpty = state.isEmpty && !state.hasError
        binding.emptyContainer.visibility = if (shouldShowEmpty) {
            View.VISIBLE
        } else {
            View.GONE
        }

        // Enhanced logging for empty state debugging
        Log.i(TAG, "EMOJI_LOADING_FLOW: ========== EMPTY STATE UPDATE ==========")
        Log.i(TAG, "EMOJI_LOADING_FLOW: Category: ${state.selectedCategory.displayName}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: isEmpty: ${state.isEmpty}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: hasError: ${state.hasError}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: isLoading: ${state.isLoading}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: displayedStyles.size: ${state.displayedStyles.size}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: allStyles.size: ${state.allStyles.size}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: shouldShowEmpty: $shouldShowEmpty")
        Log.i(TAG, "EMOJI_LOADING_FLOW: emptyContainer.visibility: ${if (shouldShowEmpty) "VISIBLE" else "GONE"}")

        if (shouldShowEmpty) {
            Log.w(TAG, "EMOJI_LOADING_FLOW: SHOWING EMPTY STATE for category: ${state.selectedCategory.displayName}")
        }
    }

    /**
     * Updates search state - refactored for new BatteryGalleryUiState
     */
    private fun updateSearchState(state: BatteryGalleryUiState) {
        binding.searchContainer.visibility = if (state.isSearchActive) {
            View.VISIBLE
        } else {
            View.GONE
        }

        if (!state.isSearchActive) {
            binding.searchEditText.text?.clear()
        }
    }

    /**
     * Updates global toggle state - refactored for new BatteryGalleryUiState
     */
    private fun updateGlobalToggle(state: BatteryGalleryUiState) {
        if (binding.globalToggleSwitch.isChecked != state.isGlobalToggleEnabled) {
            binding.globalToggleSwitch.isChecked = state.isGlobalToggleEnabled
        }
    }
    
    /**
     * Updates battery styles list - refactored for new BatteryGalleryUiState
     */
    private fun updateBatteryStyles(state: BatteryGalleryUiState) {
        Log.d(TAG, "REFACTORED_FRAGMENT: updateBatteryStyles called with ${state.displayedStyles.size} styles")
        Log.i(TAG, "EMOJI_LOADING_FLOW: ========== ADAPTER UPDATE ==========")
        Log.i(TAG, "EMOJI_LOADING_FLOW: Category: ${state.selectedCategory.displayName}")
        Log.i(TAG, "EMOJI_LOADING_FLOW: Updating adapter with ${state.displayedStyles.size} styles")

        // Log current adapter state
        Log.d(TAG, "REFACTORED_FRAGMENT: Current adapter item count: ${batteryStyleAdapter.itemCount}")

        // Log details about styles being passed to adapter
        state.displayedStyles.forEachIndexed { index, style ->
            Log.d(TAG, "REFACTORED_FRAGMENT: Style $index: name='${style.name}', id='${style.id}', premium=${style.isPremium}")
        }

        // Check if RecyclerView is properly set up
        val recyclerView = binding.emojiRecyclerView
        Log.d(TAG, "REFACTORED_FRAGMENT: RecyclerView adapter: ${recyclerView.adapter}")
        Log.d(TAG, "REFACTORED_FRAGMENT: RecyclerView visibility: ${recyclerView.visibility}")
        Log.d(TAG, "REFACTORED_FRAGMENT: RecyclerView layout manager: ${recyclerView.layoutManager}")

        batteryStyleAdapter.submitList(state.displayedStyles)
        Log.i(TAG, "EMOJI_LOADING_FLOW: Submitted ${state.displayedStyles.size} styles to adapter")

        Log.d(TAG, "REFACTORED_FRAGMENT: updateBatteryStyles completed. Adapter now has ${batteryStyleAdapter.itemCount} items")

        // Log if adapter is empty for debugging
        if (state.displayedStyles.isEmpty()) {
            Log.w(TAG, "EMOJI_LOADING_FLOW: WARNING - Adapter updated with empty list for category: ${state.selectedCategory.displayName}")
        }
    }

    /**
     * Updates category selection - refactored for new BatteryGalleryUiState
     */
    private fun updateCategorySelection(state: BatteryGalleryUiState) {
        val newIndex = categories.indexOf(state.selectedCategory)
        if (newIndex != -1) {
            val oldIndex = selectedCategoryIndex

            // Always ensure adapter is in sync with the state, even if selectedCategoryIndex was already updated
            if (categoryAdapter.getSelectedIndex() != newIndex) {
                categoryAdapter.updateSelection(newIndex)
                Log.d(TAG, "REFACTORED_FRAGMENT: Category adapter selection synced from ${categoryAdapter.getSelectedIndex()} to $newIndex")
            }

            // Update local index if needed
            if (selectedCategoryIndex != newIndex) {
                selectedCategoryIndex = newIndex
                Log.d(TAG, "REFACTORED_FRAGMENT: Fragment selectedCategoryIndex updated from $oldIndex to $selectedCategoryIndex")
            }

            // Smooth scroll to selected category if needed
            smoothScrollToSelectedCategory(selectedCategoryIndex)

            Log.d(TAG, "REFACTORED_FRAGMENT: Category selection UI state confirmed: ${state.selectedCategory.displayName} (index: $selectedCategoryIndex)")
        }
    }

    /**
     * Smoothly scrolls to the selected category for better UX
     */
    private fun smoothScrollToSelectedCategory(index: Int) {
        try {
            binding.categoryRecyclerView.smoothScrollToPosition(index)
        } catch (exception: Exception) {
            Log.e(TAG, "Error scrolling to selected category", exception)
        }
    }
    
    /**
     * Updates refresh state - refactored for new BatteryGalleryUiState
     */
    private fun updateRefreshState(state: BatteryGalleryUiState) {
        // binding.swipeRefreshLayout.isRefreshing = state.isRefreshing
        // Commented out due to SwipeRefreshLayout dependency issue
        Log.d(TAG, "REFACTORED_FRAGMENT: Refresh state: ${state.isRefreshing}")
    }

    /**
     * Handles battery style selection by delegating to ViewModel
     */
    private fun handleStyleSelection(style: BatteryStyle) {
        Log.d(TAG, "REFACTORED_FRAGMENT: handleStyleSelection called for style: ${style.name}, isPremium: ${style.isPremium}")

        // Delegate to ViewModel for MVI-compliant navigation handling
        viewModel.handleEvent(BatteryGalleryEvent.SelectBatteryStyle(style))
    }
    
    /**
     * Shows filter options dialog
     */
    private fun showFilterOptions() {
        // TODO: Implement filter options dialog
        Log.d(TAG, "Filter options dialog not yet implemented")
    }
    
    /**
     * Shows info dialog
     */
    private fun showInfoDialog() {
        // TODO: Implement info dialog
        Log.d(TAG, "Info dialog not yet implemented")
    }
    
    /**
     * Shows premium unlock dialog
     */
    private fun showPremiumUnlockDialog(style: BatteryStyle) {
        Log.d(TAG, "NAVIGATION_DEBUG: Premium unlock dialog triggered for style: ${style.name}")

        // For now, show a simple toast to indicate the premium dialog would appear
        // TODO: Implement actual premium unlock dialog
        try {
            android.widget.Toast.makeText(
                requireContext(),
                "Premium feature: ${style.name}\nUpgrade to unlock this style!",
                android.widget.Toast.LENGTH_LONG
            ).show()

            Log.d(TAG, "NAVIGATION_DEBUG: Premium dialog shown successfully for: ${style.name}")
        } catch (exception: Exception) {
            Log.e(TAG, "NAVIGATION_DEBUG: Error showing premium dialog for: ${style.name}", exception)
        }
    }
    
    /**
     * Navigates to customization screen using the EmojiCustomizeActivity.
     * This provides a better user experience with proper ActionBar navigation and theme integration.
     */
    private fun navigateToCustomization(style: BatteryStyle) {
        Log.d(TAG, "EMOJI_NAVIGATION: navigateToCustomization called for style: ${style.name}")

        try {
            // Extract category ID from the selected style
            val categoryId = style.category.toCategoryId()
            Log.d(TAG, "EMOJI_NAVIGATION: Category ID for style ${style.name}: $categoryId")
            
            // Create intent for EmojiCustomizeActivity with category information
            val intent = com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity.createIntent(
                requireContext(),
                style,
                categoryId
            )
            Log.d(TAG, "EMOJI_NAVIGATION: Created EmojiCustomizeActivity intent for style: ${style.name}, categoryId: $categoryId")

            // Start the Activity with a smooth transition
            startActivity(intent)

            // Add custom transition animation for better UX
            requireActivity().overridePendingTransition(
                R.anim.slide_in_left,
                R.anim.slide_out_right
            )

            Log.d(TAG, "EMOJI_NAVIGATION: Successfully launched EmojiCustomizeActivity")
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_NAVIGATION: Error launching EmojiCustomizeActivity", exception)
            
            // Show error to user
            showToast("Failed to open customization screen: ${exception.message}")
        }
    }    
    /**
     * Updates category tabs with remote config data.
     * Replaces hardcoded categories with Firebase Remote Config categories.
     * Handles "NEW" label display and maintains existing selection state.
     */
    private fun updateCategoryTabs(remoteCategories: List<EmojiCategory>) {
        try {
            Log.d(TAG, "REMOTE_CONFIG: Starting category tabs update with ${remoteCategories.size} remote categories")
            Log.d(TAG, "REMOTE_CONFIG: Current fragment state - categories: ${categories.size}, selectedIndex: $selectedCategoryIndex")

            // Convert remote categories to BatteryStyleCategory for compatibility
            val compatibleCategories = mutableListOf<BatteryStyleCategory>()
            val newCategoryFlags = mutableMapOf<BatteryStyleCategory, Boolean>()

            remoteCategories.forEach { remoteCategory ->
                val batteryStyleCategory = remoteCategory.toBatteryStyleCategory()
                if (batteryStyleCategory != null) {
                    compatibleCategories.add(batteryStyleCategory)
                    newCategoryFlags[batteryStyleCategory] = remoteCategory.shouldShowNewLabel()
                    Log.d(TAG, "REMOTE_CONFIG: Mapped ${remoteCategory.name} -> ${batteryStyleCategory.displayName}, isNew=${remoteCategory.isNew}")
                } else {
                    Log.w(TAG, "REMOTE_CONFIG: No mapping found for category: ${remoteCategory.id}")
                }
            }

            if (compatibleCategories.isNotEmpty()) {
                Log.d(TAG, "REMOTE_CONFIG: Successfully mapped ${compatibleCategories.size} compatible categories")

                // Log the categories that will be used
                compatibleCategories.forEachIndexed { index, category ->
                    val isNew = newCategoryFlags[category] ?: false
                    Log.d(TAG, "REMOTE_CONFIG: Category $index: ${category.displayName} (isNew=$isNew)")
                }

                // Update the fragment's categories with remote config data
                val oldCategories = categories.toList() // Keep reference to old categories
                categories = compatibleCategories
                isUsingRemoteConfigCategories = true

                Log.d(TAG, "REMOTE_CONFIG: Updated fragment categories from ${oldCategories.size} to ${categories.size}")
                Log.d(TAG, "REMOTE_CONFIG: Old categories: ${oldCategories.map { it.displayName }}")
                Log.d(TAG, "REMOTE_CONFIG: New categories: ${categories.map { it.displayName }}")

                // Preserve selection if possible, otherwise reset to first category
                val currentSelectedCategory = if (selectedCategoryIndex < oldCategories.size) {
                    oldCategories[selectedCategoryIndex]
                } else {
                    null
                }

                val newSelectedIndex = if (currentSelectedCategory != null && categories.contains(currentSelectedCategory)) {
                    categories.indexOf(currentSelectedCategory)
                } else {
                    0 // Reset to first category
                }

                Log.d(TAG, "REMOTE_CONFIG: Selection update - old index: $selectedCategoryIndex, new index: $newSelectedIndex")
                selectedCategoryIndex = newSelectedIndex

                // Update the CategoryAdapter with new categories using DiffUtil
                updateCategoryAdapter()

                // Update ViewModel with the new selected category if it changed
                if (newSelectedIndex < categories.size) {
                    val newSelectedCategory = categories[newSelectedIndex]
                    Log.d(TAG, "REMOTE_CONFIG: Updating ViewModel with selected category: ${newSelectedCategory.displayName}")
                    viewModel.handleEvent(BatteryGalleryEvent.SelectCategory(newSelectedCategory))
                }

            } else {
                Log.w(TAG, "REMOTE_CONFIG: No compatible categories found, keeping existing categories")
                Log.d(TAG, "REMOTE_CONFIG: Existing categories: ${categories.map { it.displayName }}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "REMOTE_CONFIG: Error updating category tabs", e)
        }
    }

    /**
     * Updates the CategoryAdapter with new categories using DiffUtil for efficient updates.
     * Maintains the existing selection state and visual feedback patterns.
     */
    private fun updateCategoryAdapter() {
        try {
            Log.d(TAG, "REMOTE_CONFIG: Updating CategoryAdapter with ${categories.size} categories using DiffUtil")
            Log.d(TAG, "REMOTE_CONFIG: Selected index: $selectedCategoryIndex")

            // Initialize adapter if not already created
            if (!::categoryAdapter.isInitialized) {
                initializeCategoryAdapter()
            }

            // Update the adapter with new categories using DiffUtil
            categoryAdapter.submitList(categories) {
                // Callback executed after DiffUtil completes the update
                Log.d(TAG, "REMOTE_CONFIG: DiffUtil update completed for ${categories.size} categories")

                // Ensure the RecyclerView scrolls to show the selected category
                if (isAdded && view != null && selectedCategoryIndex < categories.size) {
                    binding.categoryRecyclerView.scrollToPosition(selectedCategoryIndex)
                    Log.d(TAG, "REMOTE_CONFIG: Scrolled to selected category at index $selectedCategoryIndex")
                }
            }

            // Update selection in adapter
            categoryAdapter.updateSelection(selectedCategoryIndex)

        } catch (e: Exception) {
            Log.e(TAG, "REMOTE_CONFIG: Error updating CategoryAdapter", e)
        }
    }

    /**
     * Initializes the CategoryAdapter with DiffUtil support.
     * Called once when the adapter is first needed.
     */
    private fun initializeCategoryAdapter() {
        try {
            Log.d(TAG, "REMOTE_CONFIG: Initializing CategoryAdapter with DiffUtil support")

            categoryAdapter = CategoryAdapter(selectedCategoryIndex) { index ->
                Log.d(TAG, "REMOTE_CONFIG: Category selected from adapter: $index")

                // Provide immediate visual feedback before state update
                if (index != selectedCategoryIndex) {
                    val oldIndex = selectedCategoryIndex
                    selectedCategoryIndex = index
                    val selectedCategory = categories[index]

                    // Immediately update adapter for instant visual feedback
                    categoryAdapter.updateSelection(selectedCategoryIndex)

                    // Trigger ViewModel event for state management
                    viewModel.handleEvent(BatteryGalleryEvent.SelectCategory(selectedCategory))

                    Log.d(TAG, "REMOTE_CONFIG: Category selection completed: ${selectedCategory.displayName} (immediate visual update from $oldIndex to $selectedCategoryIndex)")
                }
            }

            // Attach adapter to RecyclerView
            if (isAdded && view != null) {
                binding.categoryRecyclerView.adapter = categoryAdapter
                Log.d(TAG, "REMOTE_CONFIG: CategoryAdapter initialized and attached to RecyclerView")
            }
        } catch (e: Exception) {
            Log.e(TAG, "REMOTE_CONFIG: Error initializing CategoryAdapter", e)
        }
    }

    /**
     * Sets up banner ad
     */
    private fun setupBannerAd() {
        try {
            // TODO: Integrate with ApplovinBannerAdManager
            Log.d(TAG, "Banner ad setup not yet implemented")
        } catch (exception: Exception) {
            Log.e(TAG, "Error setting up banner ad", exception)
        }
    }

    /**
     * Debug method to test navigation for all items
     */
    private fun testAllItemsNavigation() {
        val currentState = viewModel.uiState.value
        val styles = currentState.displayedStyles

        Log.d(TAG, "CLICK_DEBUG: Testing navigation for ${styles.size} items")

        styles.forEachIndexed { index, style ->
            Log.d(TAG, "CLICK_DEBUG: Testing item $index: ${style.name}, isPremium: ${style.isPremium}")

            // Simulate the same logic as the adapter click
            try {
                if (style.isPremium) {
                    Log.d(TAG, "CLICK_DEBUG: Item $index would show premium dialog")
                } else {
                    Log.d(TAG, "CLICK_DEBUG: Item $index would navigate to EmojiCustomizeActivity")
                    // Test that activity intent can be created without launching it
                    val intent = com.tqhit.battery.one.features.emoji.presentation.customize.EmojiCustomizeActivity.createIntent(
                        requireContext(),
                        style
                    )
                    Log.d(TAG, "CLICK_DEBUG: Item $index - EmojiCustomizeActivity intent created successfully")
                }
            } catch (exception: Exception) {
                Log.e(TAG, "CLICK_DEBUG: Item $index - Error testing navigation", exception)
            }
        }
    }

    // NEW: Effect handling methods for the refactored MVI architecture

    /**
     * Shows permission dialog
     */
    private fun showPermissionDialog() {
        Log.d(TAG, "REFACTORED_FRAGMENT: Showing permission dialog")
        // TODO: Implement permission dialog
        showToast("Permission dialog would be shown here")
    }

    /**
     * Shows error dialog
     */
    private fun showErrorDialog(message: String, isRetryable: Boolean) {
        Log.d(TAG, "REFACTORED_FRAGMENT: Showing error dialog: $message (retryable: $isRetryable)")
        // TODO: Implement error dialog
        showToast("Error: $message")
    }

    /**
     * Shows toast message
     */
    private fun showToast(message: String) {
        Log.d(TAG, "REFACTORED_FRAGMENT: Showing toast: $message")
        try {
            android.widget.Toast.makeText(requireContext(), message, android.widget.Toast.LENGTH_SHORT).show()
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_FRAGMENT: Error showing toast", exception)
        }
    }

    /**
     * Shows snackbar message
     */
    private fun showSnackbar(message: String, actionText: String?, action: (() -> Unit)?) {
        Log.d(TAG, "REFACTORED_FRAGMENT: Showing snackbar: $message")
        // TODO: Implement snackbar with action
        showToast(message)
    }

    /**
     * Shows success message
     */
    private fun showSuccessMessage(message: String) {
        Log.d(TAG, "REFACTORED_FRAGMENT: Showing success message: $message")
        showToast("✓ $message")
    }

    /**
     * Shows error message
     */
    private fun showErrorMessage(message: String) {
        Log.d(TAG, "REFACTORED_FRAGMENT: Showing error message: $message")
        showToast("✗ $message")
    }

    /**
     * Requests accessibility permissions using enhanced dialog flow
     */
    private fun requestAccessibilityPermissions() {
        Log.d(TAG, "REFACTORED_FRAGMENT: Requesting accessibility permissions with enhanced dialog")
        try {
            EmojiOverlayPermissionManager.showAccessibilityPermissionFlow(
                context = requireActivity(),
                onGranted = {
                    Log.d(TAG, "REFACTORED_FRAGMENT: Permission granted")
                    viewModel.handlePermissionResult(true)
                },
                onDenied = {
                    Log.d(TAG, "REFACTORED_FRAGMENT: Permission denied")
                    viewModel.handlePermissionResult(false)
                }
            )
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_FRAGMENT: Error requesting permissions", exception)
            // Fallback to legacy method if enhanced dialog fails
            EmojiOverlayPermissionManager.requestAccessibilityPermission(
                context = requireActivity(),
                onGranted = {
                    Log.d(TAG, "REFACTORED_FRAGMENT: Permission granted (fallback)")
                    viewModel.handlePermissionResult(true)
                },
                onDenied = {
                    Log.d(TAG, "REFACTORED_FRAGMENT: Permission denied (fallback)")
                    viewModel.handlePermissionResult(false)
                }
            )
        }
    }

    /**
     * Opens accessibility settings
     */
    private fun openAccessibilitySettings() {
        Log.d(TAG, "REFACTORED_FRAGMENT: Opening accessibility settings")
        try {
            EmojiOverlayPermissionManager.openAccessibilitySettings(requireContext())
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_FRAGMENT: Error opening accessibility settings", exception)
        }
    }
}
