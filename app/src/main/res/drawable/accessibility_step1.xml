<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">

    <!-- Background -->
    <path
        android:fillColor="#f4f4f4"
        android:pathData="M0,0 L200,0 L200,200 L0,200 Z"/>

    <!-- Phone outline -->
    <path
        android:fillColor="#000000"
        android:strokeColor="#000000"
        android:strokeWidth="2"
        android:pathData="M60,40 L140,40 Q150,40 150,50 L150,150 Q150,160 140,160 L60,160 Q50,160 50,150 L50,50 Q50,40 60,40 Z"/>

    <!-- Screen -->
    <path
        android:fillColor="#ffffff"
        android:pathData="M60,50 L140,50 L140,150 L60,150 Z"/>

    <!-- Settings icon -->
    <path
        android:fillColor="#000000"
        android:pathData="M90,70 L110,70 L110,80 L90,80 Z"/>

    <!-- Text lines representing settings -->
    <path
        android:fillColor="#989898"
        android:pathData="M70,90 L130,90 L130,95 L70,95 Z"/>
    <path
        android:fillColor="#989898"
        android:pathData="M70,100 L130,100 L130,105 L70,105 Z"/>
    <path
        android:fillColor="#109d58"
        android:pathData="M70,110 L130,110 L130,115 L70,115 Z"/>
    <path
        android:fillColor="#989898"
        android:pathData="M70,120 L130,120 L130,125 L70,125 Z"/>

    <!-- Step number circle -->
    <path
        android:fillColor="#109d58"
        android:pathData="M30,15 A15,15 0 1,1 30,45 A15,15 0 1,1 30,15 Z"/>

</vector>
