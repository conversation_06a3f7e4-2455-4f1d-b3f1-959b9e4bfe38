<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">

    <!-- Background -->
    <path
        android:fillColor="#f4f4f4"
        android:pathData="M0,0 L200,0 L200,200 L0,200 Z"/>

    <!-- Phone outline -->
    <path
        android:fillColor="#000000"
        android:strokeColor="#000000"
        android:strokeWidth="2"
        android:pathData="M60,40 L140,40 Q150,40 150,50 L150,150 Q150,160 140,160 L60,160 Q50,160 50,150 L50,50 Q50,40 60,40 Z"/>

    <!-- Screen -->
    <path
        android:fillColor="#ffffff"
        android:pathData="M60,50 L140,50 L140,150 L60,150 Z"/>

    <!-- Permission dialog -->
    <path
        android:fillColor="#f4f4f4"
        android:strokeColor="#000000"
        android:strokeWidth="1"
        android:pathData="M70,80 L130,80 L130,130 L70,130 Z"/>

    <!-- Dialog title -->
    <path
        android:fillColor="#000000"
        android:pathData="M75,85 L125,85 L125,90 L75,90 Z"/>

    <!-- Dialog text -->
    <path
        android:fillColor="#989898"
        android:pathData="M75,95 L125,95 L125,98 L75,98 Z"/>
    <path
        android:fillColor="#989898"
        android:pathData="M75,100 L125,100 L125,103 L75,103 Z"/>
    <path
        android:fillColor="#989898"
        android:pathData="M75,105 L125,105 L125,108 L75,108 Z"/>

    <!-- Toggle switch (enabled) -->
    <path
        android:fillColor="#109d58"
        android:pathData="M85,115 L115,115 Q120,115 120,120 Q120,125 115,125 L85,125 Q80,125 80,120 Q80,115 85,115 Z"/>

    <!-- Toggle switch thumb -->
    <path
        android:fillColor="#ffffff"
        android:pathData="M110,116 A4,4 0 1,1 110,124 A4,4 0 1,1 110,116 Z"/>

    <!-- Step number circle -->
    <path
        android:fillColor="#109d58"
        android:pathData="M30,15 A15,15 0 1,1 30,45 A15,15 0 1,1 30,15 Z"/>

</vector>
